//
//  ItemCard.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import Foundation
import SwiftUI

// 卡片模型
struct ItemCard: Codable, Identifiable, Equatable {
    let id: UUID
    let tags: [String]
    let description: String
    let title: String
    let imageFileName: String  // 本地存储的文件名
    let imageURL: String // COS返回的URL，供共享使用
    let createdAt: Date // 创建时间戳
    let position: String // 创建卡片时的位置，支持后续自己修改
    let remark: String? // 由用户填写的备注信息（可选）

    // 从 Data 加载图像
    var image: UIImage? {
            let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
                .first!.appendingPathComponent(imageFileName)
            if let data = try? Data(contentsOf: url) {
                return UIImage(data: data)
            }
            return nil
        }

    // 格式化创建时间显示
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: createdAt)
    }
}
