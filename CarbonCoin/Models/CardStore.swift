//
//  CardStore.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import Foundation
import SwiftUI

// MARK: - 卡片存储管理
class CardStore: ObservableObject {
    @Published var cards: [ItemCard] = []
    private let userDefaultsKey = "SavedCards"
    
    init() {
        loadCards()
    }
    
    // 保存卡片到 UserDefaults 和图像到文件系统
    func saveCard(tags: [String], description: String, title: String, imageData: Data, location: String = "", latitude: Double? = nil, longitude: Double? = nil, remark: String? = nil, imageURL: String = "") {
        let id = UUID()
        let imageFileName = saveImageToDocuments(imageData: imageData, id: id)

        // 创建初始卡片（本地保存）
        var card = ItemCard(
            id: id,
            tags: tags,
            description: description,
            title: title,
            imageFileName: imageFileName,
            imageURL: imageURL,
            createdAt: Date(),
            authorId: "current_user", // TODO: 从用户管理器获取实际用户ID
            location: location,
            latitude: latitude,
            longitude: longitude,
            remark: remark
        )

        // 先添加到本地存储
        cards.append(card)
        saveCards()

        // 异步上传图片到COS
        Task {
            await uploadImageToCOS(for: card, imageData: imageData)
        }
    }

    // 异步上传图片到COS并更新卡片URL
    @MainActor
    private func uploadImageToCOS(for card: ItemCard, imageData: Data) async {
        do {
            let imageURL = try await ImageShareService.shared.uploadImage(imageData)

            // 更新卡片的imageURL
            if let index = cards.firstIndex(where: { $0.id == card.id }) {
                let updatedCard = ItemCard(
                    id: card.id,
                    tags: card.tags,
                    description: card.description,
                    title: card.title,
                    imageFileName: card.imageFileName,
                    imageURL: imageURL,
                    createdAt: card.createdAt,
                    authorId: card.authorId,
                    location: card.location,
                    latitude: card.latitude,
                    longitude: card.longitude,
                    remark: card.remark
                )
                cards[index] = updatedCard
                saveCards()
                print("卡片图片上传成功，URL已更新: \(imageURL)")
            }
        } catch {
            print("图片上传失败: \(error.localizedDescription)")
            // 上传失败时保持本地存储，imageURL为空
        }
    }
    
    // 更新现有卡片
    func updateCard(_ updatedCard: ItemCard) {
        if let index = cards.firstIndex(where: { $0.id == updatedCard.id }) {
            cards[index] = updatedCard
            saveCards()
        }
    }
    
    // 删除卡片
    func deleteCard(_ card: ItemCard) {
        // 删除图像文件
        if !card.imageFileName.isEmpty {
            let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
                .first!.appendingPathComponent(card.imageFileName)
            try? FileManager.default.removeItem(at: url)
        }
        
        // 从数组中移除
        cards.removeAll { $0.id == card.id }
        saveCards()
    }
    
    // 从 UserDefaults 加载卡片
    private func loadCards() {
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey),
           let savedCards = try? JSONDecoder().decode([ItemCard].self, from: data) {
            cards = savedCards
        }
    }
    
    // 保存卡片到 UserDefaults
    private func saveCards() {
        if let data = try? JSONEncoder().encode(cards) {
            UserDefaults.standard.set(data, forKey: userDefaultsKey)
        }
    }
    
    // 保存图像到 Documents 目录
    private func saveImageToDocuments(imageData: Data, id: UUID) -> String {
        let fileName = "\(id.uuidString).png"
        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent(fileName)
        try? imageData.write(to: url)
        return fileName // 只返回文件名
    }
}
