//
//  UserLocationManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import Foundation
import CoreLocation
import SwiftUI

// MARK: - 用户位置管理器
@MainActor
class UserLocationManager: NSObject, ObservableObject, @preconcurrency CLLocationManagerDelegate {
    
    // MARK: - Published Properties
    /// 用户当前位置坐标
    @Published var userLocation: CLLocationCoordinate2D?
    
    /// 位置权限状态
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined
    
    /// 位置更新错误信息
    @Published var locationError: String?
    
    /// 是否正在更新位置
    @Published var isUpdatingLocation: Bool = false
    
    /// 用户当前朝向
       @Published var userHeading: CLLocationDirection?

    /// 当前位置的地址字符串
    @Published var currentLocationString: String?

    /// 当前位置对象
    @Published var currentLocation: CLLocation?

    // MARK: - Private Properties
    private let locationManager = CLLocationManager()
    private let geocoder = CLGeocoder()
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupLocationManager()
    }
    
    // MARK: - Setup
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.distanceFilter = 5 // 5米更新一次
        locationManager.headingFilter = 1 // 1度更新一次朝向
        authorizationStatus = locationManager.authorizationStatus
    }
    
    // MARK: - Public Methods
    
    /// 请求位置权限
    func requestLocationPermission() {
        switch authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .denied, .restricted:
            // 引导用户到设置页面
            locationError = "位置权限被拒绝，请在设置中开启位置权限"
        case .authorizedWhenInUse, .authorizedAlways:
            startLocationUpdates()
        @unknown default:
            locationError = "未知的位置权限状态"
        }
    }
    
    /// 开始位置更新
    func startLocationUpdates() {
        // TODO:
        guard CLLocationManager.locationServicesEnabled() else {
            locationError = "位置服务未开启"
            return
        }
        
        switch authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways:
            isUpdatingLocation = true
            locationManager.startUpdatingLocation()
            locationManager.startUpdatingHeading()
            locationError = nil
        case .notDetermined:
            requestLocationPermission()
        case .denied, .restricted:
            locationError = "位置权限被拒绝，请在设置中开启位置权限"
        @unknown default:
            locationError = "未知的位置权限状态"
        }
    }
    
    /// 停止位置更新
    func stopLocationUpdates() {
        isUpdatingLocation = false
        locationManager.stopUpdatingLocation()
        locationManager.stopUpdatingHeading()
    }
    
    /// 获取一次性位置
    func requestLocation() {
        guard CLLocationManager.locationServicesEnabled() else {
            locationError = "位置服务未开启"
            return
        }

        switch authorizationStatus {
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.requestLocation()
            locationError = nil
        case .notDetermined:
            requestLocationPermission()
        case .denied, .restricted:
            locationError = "位置权限被拒绝，请在设置中开启位置权限"
        @unknown default:
            locationError = "未知的位置权限状态"
        }
    }

    /// 获取当前位置的地址字符串（用于创建卡片）
    func getCurrentLocationInfo() async -> (location: CLLocation?, locationString: String) {
        // 如果已有当前位置，直接使用
        if let currentLoc = currentLocation {
            let locationString = await reverseGeocodeLocation(currentLoc)
            return (currentLoc, locationString)
        }

        // 否则请求一次性位置
        return await withCheckedContinuation { continuation in
            guard CLLocationManager.locationServicesEnabled() else {
                continuation.resume(returning: (nil, "位置服务未开启"))
                return
            }

            switch authorizationStatus {
            case .authorizedWhenInUse, .authorizedAlways:
                // 创建临时的位置管理器来获取一次性位置
                let tempLocationManager = CLLocationManager()
                let delegate = OneTimeLocationDelegate { [weak self] location in
                    Task { @MainActor in
                        if let location = location {
                            let locationString = await self?.reverseGeocodeLocation(location) ?? "位置未知"
                            continuation.resume(returning: (location, locationString))
                        } else {
                            continuation.resume(returning: (nil, "位置获取失败"))
                        }
                    }
                }
                tempLocationManager.delegate = delegate
                tempLocationManager.desiredAccuracy = kCLLocationAccuracyBest
                tempLocationManager.requestLocation()
            default:
                continuation.resume(returning: (nil, "位置权限未授权"))
            }
        }
    }

    /// 反向地理编码获取地址字符串
    private func reverseGeocodeLocation(_ location: CLLocation) async -> String {
        do {
            let placemarks = try await geocoder.reverseGeocodeLocation(location)
            guard let placemark = placemarks.first else {
                return "位置未知"
            }

            // 构建位置字符串
            var locationComponents: [String] = []

            if let city = placemark.locality {
                locationComponents.append(city)
            }
            if let district = placemark.subLocality {
                locationComponents.append(district)
            }
            if let street = placemark.thoroughfare {
                locationComponents.append(street)
            }

            return locationComponents.isEmpty ? "位置未知" : locationComponents.joined(separator: " ")

        } catch {
            print("反向地理编码失败: \(error.localizedDescription)")
            return "位置未知"
        }
    }
    
    // MARK: - CLLocationManagerDelegate
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }

        // 过滤精度较差的位置数据
        guard location.horizontalAccuracy < 100 else { return }

        userLocation = location.coordinate
        currentLocation = location
        locationError = nil

        // 异步进行反向地理编码
        Task {
            let locationString = await reverseGeocodeLocation(location)
            await MainActor.run {
                self.currentLocationString = locationString
            }
        }

        print("位置更新: \(location.coordinate.latitude), \(location.coordinate.longitude)")
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateHeading newHeading: CLHeading) {
            userHeading = newHeading.trueHeading // 更新朝向
        }
    
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        isUpdatingLocation = false
        
        if let clError = error as? CLError {
            switch clError.code {
            case .locationUnknown:
                locationError = "无法获取位置信息"
            case .denied:
                locationError = "位置权限被拒绝"
            case .network:
                locationError = "网络错误，无法获取位置"
            default:
                locationError = "位置获取失败: \(clError.localizedDescription)"
            }
        } else {
            locationError = "位置获取失败: \(error.localizedDescription)"
        }
        
        print("位置获取失败: \(error)")
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        authorizationStatus = status
        
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            locationError = nil
            if isUpdatingLocation {
                startLocationUpdates()
            }
        case .denied, .restricted:
            stopLocationUpdates()
            locationError = "位置权限被拒绝，请在设置中开启位置权限"
        case .notDetermined:
            locationError = nil
        @unknown default:
            locationError = "未知的位置权限状态"
        }
        
        print("位置权限状态变更: \(status.rawValue)")
    }
}

// MARK: - CLAuthorizationStatus Extension
extension CLAuthorizationStatus {
    /// 是否已授权
    var isAuthorized: Bool {
        return self == .authorizedWhenInUse || self == .authorizedAlways
    }
    
    /// 状态描述
    var description: String {
        switch self {
        case .notDetermined:
            return "未确定"
        case .restricted:
            return "受限制"
        case .denied:
            return "已拒绝"
        case .authorizedAlways:
            return "始终允许"
        case .authorizedWhenInUse:
            return "使用时允许"
        @unknown default:
            return "未知状态"
        }
    }
}

// MARK: - 一次性位置获取辅助类
private class OneTimeLocationDelegate: NSObject, CLLocationManagerDelegate {
    private let completion: (CLLocation?) -> Void

    init(completion: @escaping (CLLocation?) -> Void) {
        self.completion = completion
        super.init()
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        manager.stopUpdatingLocation()
        completion(locations.last)
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        manager.stopUpdatingLocation()
        completion(nil)
    }
}
