//
//  LocationManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/29.
//

import Foundation
import SwiftUI
import CoreLocation

// MARK: - 位置管理器
class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    private let locationManager = CLLocationManager()
    @Published var currentLocation: CLLocation?
    @Published var currentLocationString: String?

    override init() {
        super.init()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        requestLocationPermission()
    }

    // MARK: 请求位置权限
    private func requestLocationPermission() {
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse, .authorizedAlways:
            startLocationUpdates()
        default:
            break
        }
    }

    // 开始位置更新
    private func startLocationUpdates() {
        guard locationManager.authorizationStatus == .authorizedWhenInUse ||
              locationManager.authorizationStatus == .authorizedAlways else { return }

        locationManager.startUpdatingLocation()
    }

    // MARK: - CLLocationManagerDelegate
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        currentLocation = location

        // 反向地理编码获取地址
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
            DispatchQueue.main.async {
                if let placemark = placemarks?.first {
                    // 构建位置字符串
                    var locationComponents: [String] = []

                    if let city = placemark.locality {
                        locationComponents.append(city)
                    }
                    if let district = placemark.subLocality {
                        locationComponents.append(district)
                    }
                    if let street = placemark.thoroughfare {
                        locationComponents.append(street)
                    }

                    self?.currentLocationString = locationComponents.isEmpty ?
                        "位置未知" : locationComponents.joined(separator: " ")
                } else {
                    self?.currentLocationString = "位置未知"
                }
            }
        }

        // 停止位置更新以节省电量
        locationManager.stopUpdatingLocation()
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("位置获取失败: \(error.localizedDescription)")
        currentLocationString = "位置未知"
    }

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            startLocationUpdates()
        case .denied, .restricted:
            currentLocationString = "位置权限被拒绝"
        default:
            break
        }
    }
}
